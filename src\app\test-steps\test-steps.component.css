/* ===== CONTENEUR CENTRÉ ===== */
.centered-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.stepper-wrapper {
  width: 100%;
  max-width: 800px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-top: 20px;
}

/* ===== STYLES EXISTANTS ===== */
.step-label {
  margin-left: 8px;
  font-weight: 600;
}

.step-content {
  margin: 16px 0;
}

.full-width {
  width: 100%;
}

.step-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Styles pour le bouton d'ouverture de l'IDE */
.mt-3 {
  margin-top: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.text-muted {
  color: #6c757d;
}

.small {
  font-size: 0.875rem;
}

/* Animation pour le bouton */
button[mat-raised-button] {
  transition: transform 0.2s ease-in-out;
}

button[mat-raised-button]:hover:not([disabled]) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Animation pour l'icône de chargement */
.spinner {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Style pour le code */
code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9em;
  color: #e91e63;
}

/* Styles pour la sélection de projet */
.project-selection {
  margin-top: 16px;
  margin-bottom: 16px;
}

/* Styles pour la section projet */
.project-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border-left: 4px solid #3f51b5;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.project-info h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  color: #3f51b5;
}

.project-path {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;
}

.project-path mat-icon {
  color: #607d8b;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

.project-path code {
  flex: 1;
  white-space: nowrap;
  overflow-x: auto;
  max-width: 100%;
  padding: 6px 8px;
  background-color: #e8eaf6;
  color: #3949ab;
  border-radius: 4px;
}

/* Style pour le bouton d'ouverture de l'IDE */
.open-ide-button {
  margin-top: 8px;
  margin-bottom: 8px;
  padding: 8px 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* Styles pour la section d'upload de fichiers */
.file-upload-section {
  margin: 16px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.upload-container {
  background-color: #f8f9fa;
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.upload-container:hover {
  border-color: #3f51b5;
  background-color: #e8eaf6;
}

.upload-button {
  padding: 12px 24px;
  font-weight: 500;
}

/* Styles pour la liste de fichiers */
.file-list {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
  border-left: 4px solid #3f51b5;
}

.file-list h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #3f51b5;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.file-item:hover {
  background-color: #e8eaf6;
}

.file-item mat-icon {
  margin-right: 8px;
  color: #607d8b;
}

.file-name {
  flex: 1;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #757575;
  margin: 0 16px;
  font-size: 0.9em;
}

/* ===== STYLES POUR LA NOUVELLE INTERFACE SIMPLIFIÉE ===== */

/* Configuration du test */
.test-config-section {
  margin: 16px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.test-config-section h3 {
  color: #3f51b5;
  margin-bottom: 20px;
  font-weight: 600;
}

/* Tags suggérés */
.suggested-tags {
  margin: 16px 0;
}

.suggested-tags h4 {
  color: #5c6bc0;
  margin-bottom: 12px;
  font-size: 1em;
}

.tag-chips mat-chip {
  margin: 4px;
  cursor: pointer;
  background-color: #e8eaf6;
  color: #3f51b5;
  transition: all 0.3s ease;
}

.tag-chips mat-chip:hover {
  background-color: #3f51b5;
  color: white;
}

.tag-chips mat-chip.selected {
  background-color: #3f51b5;
  color: white;
  font-weight: 600;
}

/* Informations utilisateur */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  color: #666;
}

.user-info mat-icon {
  color: #3f51b5;
}

/* Aperçu de la configuration */
.config-preview {
  margin-top: 20px;
  animation: slideIn 0.3s ease-in-out;
}

.config-preview h4 {
  color: #3f51b5;
  margin-bottom: 12px;
}

.preview-card {
  background: linear-gradient(135deg, #e8eaf6 0%, #f3e5f5 100%);
  border-left: 4px solid #3f51b5;
}

.preview-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.preview-info mat-icon {
  color: #3f51b5;
  margin-top: 4px;
}

.preview-info p {
  margin: 4px 0;
  font-size: 0.95em;
}

/* Configuration Maven */
.maven-config-section {
  margin: 16px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.maven-config-section h3 {
  color: #3f51b5;
  margin-bottom: 20px;
  font-weight: 600;
}

.maven-config-section h4 {
  color: #5c6bc0;
  margin: 16px 0 12px 0;
  font-weight: 500;
  font-size: 1.1em;
}

.goals-section {
  margin-bottom: 24px;
}

.goals-display {
  margin-bottom: 12px;
}

.goals-display mat-chip {
  margin: 4px;
  background-color: #e8eaf6;
  color: #3f51b5;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .centered-container {
    padding: 10px;
    min-height: auto;
  }

  .stepper-wrapper {
    max-width: 100%;
    padding: 16px;
    margin-top: 10px;
    border-radius: 8px;
  }

  .preview-info {
    flex-direction: column;
  }

  .tag-chips mat-chip {
    margin: 2px;
    font-size: 0.9em;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Responsive pour tablettes */
@media (max-width: 1024px) and (min-width: 769px) {
  .stepper-wrapper {
    max-width: 700px;
  }
}

/* Responsive pour grands écrans */
@media (min-width: 1200px) {
  .stepper-wrapper {
    max-width: 900px;
  }
}

/* ===== STYLES POUR L'EXÉCUTION ET LES LOGS EN TEMPS RÉEL ===== */

/* Section d'exécution des tests */
.test-execution-section {
  margin: 16px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.test-execution-section h3 {
  color: #3f51b5;
  margin-bottom: 20px;
  font-weight: 600;
}

/* Statut d'exécution */
.execution-status {
  margin-bottom: 16px;
}

.status-card {
  background: linear-gradient(135deg, #e8eaf6 0%, #f3e5f5 100%);
  border-left: 4px solid #3f51b5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.status-info mat-icon {
  color: #3f51b5;
  margin-top: 4px;
  font-size: 24px;
  height: 24px;
  width: 24px;
}

.status-info p {
  margin: 4px 0;
  font-size: 0.95em;
}

/* Section des logs */
.logs-section {
  margin: 16px 0;
  animation: slideIn 0.3s ease-in-out;
}

.logs-section h6 {
  color: #3f51b5;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.logs-container {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background-color: #f8f9fa !important;
  color: #333;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.logs-container:hover {
  border-color: #3f51b5;
  box-shadow: inset 0 1px 3px rgba(63, 81, 181, 0.1);
}

/* Message d'information */
.info-message {
  margin: 16px 0;
}

.info-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
  border-left: 4px solid #2196f3;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-content mat-icon {
  color: #2196f3;
  font-size: 24px;
  height: 24px;
  width: 24px;
}

.info-content p {
  margin: 0;
  color: #666;
  font-size: 0.95em;
}

/* Animation pour l'icône de synchronisation */
.spinner {
  animation: spin 1.5s linear infinite;
  color: #ff9800 !important;
}

/* Responsive pour les logs */
@media (max-width: 768px) {
  .logs-container {
    font-size: 0.8em;
    max-height: 200px;
  }

  .status-info {
    flex-direction: column;
    gap: 8px;
  }

  .info-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
