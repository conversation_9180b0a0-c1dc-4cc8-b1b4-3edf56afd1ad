<svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007bff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0056b3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="500" height="400" fill="url(#bgGradient)" rx="20"/>
  
  <!-- Laptop -->
  <rect x="100" y="150" width="300" height="180" fill="#2c3e50" rx="10"/>
  <rect x="110" y="160" width="280" height="160" fill="url(#screenGradient)" rx="5"/>
  
  <!-- Screen content -->
  <rect x="130" y="180" width="60" height="8" fill="#007bff" rx="2"/>
  <rect x="130" y="200" width="240" height="4" fill="#6c757d" rx="1"/>
  <rect x="130" y="210" width="200" height="4" fill="#6c757d" rx="1"/>
  <rect x="130" y="220" width="180" height="4" fill="#6c757d" rx="1"/>
  
  <!-- Charts -->
  <rect x="130" y="240" width="80" height="60" fill="#e9ecef" rx="3"/>
  <rect x="135" y="270" width="10" height="25" fill="#28a745"/>
  <rect x="150" y="260" width="10" height="35" fill="#007bff"/>
  <rect x="165" y="250" width="10" height="45" fill="#ffc107"/>
  <rect x="180" y="265" width="10" height="30" fill="#dc3545"/>
  
  <!-- Code lines -->
  <rect x="230" y="245" width="120" height="3" fill="#28a745" rx="1"/>
  <rect x="230" y="255" width="100" height="3" fill="#007bff" rx="1"/>
  <rect x="230" y="265" width="140" height="3" fill="#6c757d" rx="1"/>
  <rect x="230" y="275" width="90" height="3" fill="#ffc107" rx="1"/>
  <rect x="230" y="285" width="110" height="3" fill="#dc3545" rx="1"/>
  
  <!-- Keyboard -->
  <rect x="120" y="340" width="260" height="15" fill="#495057" rx="3"/>
  
  <!-- Floating elements -->
  <circle cx="80" cy="100" r="25" fill="#28a745" opacity="0.8"/>
  <text x="80" y="108" text-anchor="middle" fill="white" font-family="Arial" font-size="20" font-weight="bold">✓</text>
  
  <circle cx="420" cy="120" r="20" fill="#007bff" opacity="0.8"/>
  <text x="420" y="128" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">⚙</text>
  
  <circle cx="450" cy="250" r="18" fill="#ffc107" opacity="0.8"/>
  <text x="450" y="258" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">📊</text>
  
  <circle cx="60" cy="280" r="22" fill="#dc3545" opacity="0.8"/>
  <text x="60" y="288" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">👥</text>
  
  <!-- Title -->
  <text x="250" y="50" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold">IT Team Management</text>
  <text x="250" y="75" text-anchor="middle" fill="white" font-family="Arial" font-size="14" opacity="0.9">Gestion d'équipe et tests automatisés</text>
</svg>
