<div class="container py-4">
  <!-- Header avec bouton retour -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
      <i class="fas fa-chart-line me-2 text-primary"></i>
      Tableau de bord du test
    </h2>
    <button class="btn btn-outline-secondary" (click)="goBack()">
      @if (authService.isAdmin()) {
        <i class="fas fa-arrow-left me-2"></i>Retour aux missions
      } @else {
        <i class="fas fa-arrow-left me-2"></i>Retour à l'historique
      }

    </button>

  </div>

  <!-- États de chargement et d'erreur -->
  @if (isLoading) {
    <div class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Chargement...</span>
      </div>
      <p class="mt-3">Chargement des détails du test...</p>
    </div>
  } @else if (errorMessage) {
    <div class="alert alert-danger">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <strong>Erreur</strong>
      <p class="mb-0 mt-2">{{ errorMessage }}</p>
      <button class="btn btn-outline-danger btn-sm mt-2" (click)="loadTestSuiteDetails()">
        <i class="fas fa-redo me-1"></i>Réessayer
      </button>
    </div>
  } @else if (testSuite) {

    <!-- Informations générales -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card shadow">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
              <i class="fas fa-info-circle me-2"></i>
              Informations générales
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <h6 class="text-muted">Nom du test</h6>
                <p class="fs-5 fw-bold">{{ testSuite.name }}</p>
              </div>
              <div class="col-md-3">
                <h6 class="text-muted">Tag Cucumber</h6>
                <span class="badge bg-info fs-6">{{ testSuite.tag }}</span>
              </div>
              <div class="col-md-3">
                <h6 class="text-muted">ID</h6>
                <p class="fs-5">#{{ testSuite.id }}</p>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-md-6">
                <h6 class="text-muted">Date d'exécution</h6>
                <p class="fs-6">
                  <i class="fas fa-calendar me-2"></i>
                  {{ formatDate(testSuite.executionDate) }}
                </p>
              </div>
              <div class="col-md-3">
                <h6 class="text-muted">Durée d'exécution</h6>
                <p class="fs-6">
                  <i class="fas fa-clock me-2"></i>
                  {{ formatDuration(testSuite.executionTimeMs) }}
                </p>
              </div>
              <div class="col-md-3">
                <h6 class="text-muted">Exécuté par</h6>
                <p class="fs-6">
                  <i class="fas fa-user me-2"></i>
                  @if (testSuite.user) {
                    {{ testSuite.user.firstName }} {{ testSuite.user.lastName }}
                  } @else {
                    <span class="text-muted">Test global (système)</span>
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Informations de la mission associée (si disponible) -->
    @if (mission) {
      <div class="row mb-4">
        <div class="col-12">
          <div class="card shadow border-success">
            <div class="card-header bg-success text-white">
              <h5 class="mb-0">
                <i class="fas fa-tasks me-2"></i>
                Mission associée
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <h6 class="text-muted">ID Exigence</h6>
                  <p class="fs-6 fw-bold">{{ mission.idExigence }}</p>
                </div>
                <div class="col-md-6">
                  <h6 class="text-muted">Titre de la mission</h6>
                  <p class="fs-5 fw-bold text-success">{{ mission.titre }}</p>
                </div>
                <div class="col-md-3">
                  <h6 class="text-muted">Statut</h6>
                  <span class="badge bg-success fs-6">
                    <i class="fas fa-check me-1"></i>Terminée
                  </span>
                </div>
              </div>
              @if (mission.description) {
                <div class="row mt-3">
                  <div class="col-md-6">
                    <h6 class="text-muted">Description</h6>
                    <p class="fs-6">{{ mission.description }}</p>
                  </div>
                  @if (mission.commentaire) {
                    <div class="col-md-6">
                      <h6 class="text-muted">Commentaire</h6>
                      <p class="fs-6 text-secondary">{{ mission.commentaire }}</p>
                    </div>
                  }
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    }

    <!-- Métriques principales -->
    <div class="row mb-4">
      <!-- Taux de succès -->
      <div class="col-md-3 mb-3">
        <div class="card shadow h-100">
          <div class="card-body text-center">
            <i [class]="getSuccessRateIcon() + ' fa-3x ' + getSuccessRateColor()"></i>
            <h3 [class]="'mt-2 ' + getSuccessRateColor()">{{ testSuite.successRate }}%</h3>
            <p class="text-muted mb-0">Taux de succès</p>
          </div>
        </div>
      </div>

      <!-- Tests totaux -->
      <div class="col-md-3 mb-3">
        <div class="card shadow h-100">
          <div class="card-body text-center">
            <i class="fas fa-list-alt fa-3x text-primary"></i>
            <h3 class="mt-2 text-primary">{{ testSuite.totalTests }}</h3>
            <p class="text-muted mb-0">Tests totaux</p>
          </div>
        </div>
      </div>

      <!-- Tests réussis -->
      <div class="col-md-3 mb-3">
        <div class="card shadow h-100">
          <div class="card-body text-center">
            <i class="fas fa-check-circle fa-3x text-success"></i>
            <h3 class="mt-2 text-success">{{ testSuite.passedTests }}</h3>
            <p class="text-muted mb-0">Tests réussis</p>
          </div>
        </div>
      </div>

      <!-- Tests échoués -->
      <div class="col-md-3 mb-3">
        <div class="card shadow h-100">
          <div class="card-body text-center">
            <i class="fas fa-times-circle fa-3x text-danger"></i>
            <h3 class="mt-2 text-danger">{{ testSuite.failedTests }}</h3>
            <p class="text-muted mb-0">Tests échoués</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Graphique de répartition -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fas fa-chart-pie me-2"></i>
              Répartition des résultats
            </h6>
          </div>
          <div class="card-body">
            <div class="progress mb-3" style="height: 30px;">
              <div class="progress-bar bg-success"
                   [style.width.%]="(testSuite.passedTests / testSuite.totalTests) * 100"
                   [attr.aria-valuenow]="testSuite.passedTests"
                   aria-valuemin="0"
                   [attr.aria-valuemax]="testSuite.totalTests">
                {{ testSuite.passedTests }} réussis
              </div>
              <div class="progress-bar bg-danger"
                   [style.width.%]="(testSuite.failedTests / testSuite.totalTests) * 100"
                   [attr.aria-valuenow]="testSuite.failedTests"
                   aria-valuemin="0"
                   [attr.aria-valuemax]="testSuite.totalTests">
                {{ testSuite.failedTests }} échoués
              </div>
              <div class="progress-bar bg-warning"
                   [style.width.%]="(testSuite.skippedTests / testSuite.totalTests) * 100"
                   [attr.aria-valuenow]="testSuite.skippedTests"
                   aria-valuemin="0"
                   [attr.aria-valuemax]="testSuite.totalTests">
                {{ testSuite.skippedTests }} ignorés
              </div>
            </div>

            <!-- Légende -->
            <div class="d-flex justify-content-around">
              <small class="text-success">
                <i class="fas fa-square me-1"></i>Réussis ({{ testSuite.passedTests }})
              </small>
              <small class="text-danger">
                <i class="fas fa-square me-1"></i>Échoués ({{ testSuite.failedTests }})
              </small>
              <small class="text-warning">
                <i class="fas fa-square me-1"></i>Ignorés ({{ testSuite.skippedTests }})
              </small>
            </div>
          </div>
        </div>
      </div>

      <!-- Informations techniques -->
      <div class="col-md-6">
        <div class="card shadow">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fas fa-cogs me-2"></i>
              Informations techniques
            </h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6">
                <small class="text-muted">Tests ignorés</small>
                <p class="fw-bold">{{ testSuite.skippedTests }}</p>
              </div>
              <div class="col-6">
                <small class="text-muted">Durée moyenne par test</small>
                <p class="fw-bold">{{ formatDuration(testSuite.executionTimeMs / testSuite.totalTests) }}</p>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <small class="text-muted">Statut global</small>
                <p [class]="'fw-bold ' + getSuccessRateColor()">
                  @if (testSuite.successRate >= 90) {
                    <i class="fas fa-check-circle me-1"></i>Excellent
                  } @else if (testSuite.successRate >= 70) {
                    <i class="fas fa-exclamation-triangle me-1"></i>Acceptable
                  } @else {
                    <i class="fas fa-times-circle me-1"></i>Critique
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Logs et rapports (si disponibles) -->
    @if (testSuite.logs || testSuite.jsonReport) {
      <div class="row">
        <div class="col-12">
          <div class="card shadow">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="fas fa-file-alt me-2"></i>
                Logs et rapports
              </h6>
            </div>
            <div class="card-body">

              @if (parsedJsonReport) {
                <div class="mt-4">
                  <h6>Rapport JSON</h6>
                  <div class="cucumber-report">
                    <h1>Cucumber Test Report</h1>
                    <ng-container *ngFor="let feature of parsedJsonReport">
                      <div class="feature">
                        <h2>🔹 Feature: {{ feature.name }}</h2>
                        <ng-container *ngFor="let scenario of feature.elements">
                          <div class="scenario">
                            <h3>🧪 Scenario: {{ scenario.name }}</h3>
                            <ng-container *ngFor="let step of scenario.steps">
                              <div class="step" [ngClass]="step.result.status">
                                <strong>{{ step.keyword }}{{ step.name }}</strong><br>
                                Status: <em>{{ step.result.status }}</em>
                                <ng-container *ngIf="step.result.status === 'failed' && step.result.error_message">
                                  <pre>{{ step.result.error_message }}</pre>
                                </ng-container>
                              </div>
                            </ng-container>
                          </div>
                        </ng-container>
                      </div>
                    </ng-container>
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    }

  } @else {
    <div class="alert alert-warning">
      <i class="fas fa-exclamation-triangle me-2"></i>
      Aucun test trouvé avec cet ID.
    </div>
  }
</div>
