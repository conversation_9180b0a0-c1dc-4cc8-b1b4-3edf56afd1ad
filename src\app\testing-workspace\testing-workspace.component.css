.card {
  --main-color: #000;
  --bg-color: #8DD4EB;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  width: 300px;
  padding: 25px;
  background: var(--bg-color);
  border-radius: 20px;
  margin: 20px;
}

.card__wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  
}

.card___wrapper-acounts {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  z-index: 1;
  cursor: pointer;
}

.card___wrapper-acounts > div:nth-child(2) {
  position: absolute;
  left: 25px;
  z-index: -1;
}

.card___wrapper-acounts > div:nth-child(3) {
  position: absolute;
  left: 50px;
  z-index: -2;
}

.card__score {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 100%;
  background: var(--main-color);
}

.card__acounts {
  width: 42px;
  height: 42px;
}

.card__acounts svg {
  width: 100%;
  height: 100%;
}

.card__menu {
  width: 40px;
  height: 40px;
  background: var(--bg-color);
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.card__title {
  margin-top: 50px;
  font-weight: 900;
  font-size: 25px;
  color: var(--main-color);
}

.card__subtitle {
  margin-top: 15px;
  font-weight: 400;
  font-size: 15px;
  color: var(--main-color);
}

.card__indicator {
  margin-top: 50px;
  font-weight: 500;
  font-size: 14px;
  color: var(--main-color);
}

.card__progress progress {
  width: 100%;
  height: 4px;
  border-radius: 100px;
}

.card__progress progress::-webkit-progress-bar {
  background-color: #00000030;
  border-radius: 100px;
}

.card__progress progress::-webkit-progress-value {
  background-color: var(--main-color);
  border-radius: 100px;
}


.cta {
  border: none;
  background: none;
  cursor: pointer;
}

.cta span {
  padding-bottom: 7px;
  letter-spacing: 4px;
  font-size: 14px;
  padding-right: 15px;
  text-transform: uppercase;
}

.cta svg {
  transform: translateX(-8px);
  transition: all 0.3s ease;
}

.cta:hover svg {
  transform: translateX(0);
}

.cta:active svg {
  transform: scale(0.9);
}

.hover-underline-animation {
  position: relative;
  color: black;
  padding-bottom: 20px;
}

.hover-underline-animation:after {
  content: "";
  position: absolute;
  width: 100%;
  transform: scaleX(0);
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #000000;
  transform-origin: bottom right;
  transition: transform 0.25s ease-out;
}

.cta:hover .hover-underline-animation:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

a {
    text-decoration: none;
}

