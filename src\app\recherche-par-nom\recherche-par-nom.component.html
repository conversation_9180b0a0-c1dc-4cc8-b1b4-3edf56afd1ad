<div class="container py-4">
    <div class="col-sm-4 col-md-4 col-lg-4">
        <!-- <label>User Name</label>
        <input type="text" class="form-control" #searchTerm (keyup)="onKeyUp(searchTerm.value)">-->


        <div class="group">
            <svg class="icon" aria-hidden="true" viewBox="0 0 24 24">
                <g>
                    <path
                        d="M21.53 20.47l-3.66-3.66C19.195 15.24 20 13.214 20 11c0-4.97-4.03-9-9-9s-9 4.03-9 9 4.03 9 9 9c2.215 0 4.24-.804 5.808-2.13l3.66 3.66c.*************.53.22s.385-.073.53-.22c.295-.293.295-.767.002-1.06zM3.5 11c0-4.135 3.365-7.5 7.5-7.5s7.5 3.365 7.5 7.5-3.365 7.5-7.5 7.5-7.5-3.365-7.5-7.5z">
                    </path>
                </g>
            </svg>
            <input placeholder="Search" type="search" class="input" #searchTerm (keyup)="onKeyUp(searchTerm.value)">
        </div>

    </div>
    <div class="card shadow mb-4">
        <div class="card-body">
            <table class="table table-striped">

                <tr>
                    <th>N°</th>
                    <th>Id</th>
                    <th>Email</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th hidden>Password</th>
                    <th>Role</th>
                </tr>

                @for (usr of users; track usr.id ;let index =$index) {
                <tbody>
                    <tr>
                        <td>{{index+1}}</td>
                        <td>{{usr.id}}</td>
                        <td>{{usr.email}}</td>
                        <td>{{usr.firstName}}</td>
                        <td>{{usr.lastName}}</td>
                        <td hidden>{{usr.password}}</td>
                        <td>
                            <span *ngFor="let r of usr.roles; let i = index">
                                {{ r.name }}<span *ngIf="i < usr.roles.length - 1">, </span>
                            </span>
                        </td>
                        <!-- <td><a class="btn btn-danger" (click)="deleteUser(usr)">Delete</a></td>
                        <td><a class="btn btn-success" [routerLink]="['/updateUser',usr.id]">Update</a></td> -->
                    </tr>
                </tbody>
                }
            </table>
        </div>
    </div>