/* From Uiverse.io by Yaya12085 */
.form-container {
  width: 320px;
  border-radius: 0.75rem;
  background-color: rgb(30, 32, 37);
  padding: 2rem;
  color: rgba(243, 244, 246, 1);
}

.title {
  text-align: center;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
}

.form {
  margin-top: 1.5rem;
}

.input-group {
  margin-top: 0.25rem;
  margin-bottom: 0.5rem;

  font-size: 0.875rem;
  line-height: 1.25rem;
}

.input-group label {
  display: block;
  color: rgba(156, 163, 175, 1);
  margin-bottom: 4px;
}

.input-group input {
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid rgba(55, 65, 81, 1);
  outline: 0;
  background-color: rgba(17, 24, 39, 1);
  padding: 0.75rem 1rem;
  color: rgba(243, 244, 246, 1);
}

.input-group input:focus {
  border-color: rgba(167, 139, 250);
}

.forgot {
  display: flex;
  justify-content: flex-end;
  font-size: 0.75rem;
  line-height: 1rem;
  color: rgba(156, 163, 175,1);
  margin: 8px 0 14px 0;
}

.forgot a,.signup a {
  color: rgba(243, 244, 246, 1);
  text-decoration: none;
  font-size: 14px;
}

.forgot a:hover, .signup a:hover {
  text-decoration: underline rgba(167, 139, 250, 1);
}


.sign {
  display: block;
  width: 100%;
  background-color: rgba(167, 139, 250, 1);
  padding: 0.75rem;
  text-align: center;
  color: rgba(17, 24, 39, 1);
  border: none;
  border-radius: 0.375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sign:hover:not(:disabled) {
  background-color: rgba(147, 119, 230, 1);
  transform: translateY(-1px);
}

.sign:disabled {
  background-color: rgba(107, 114, 128, 1);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Styles pour les messages d'erreur */
.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(239, 68, 68, 1);
  font-size: 0.875rem;
  animation: slideIn 0.3s ease-out;
}

.error-icon {
  font-size: 1rem;
}

/* Styles pour les champs en erreur */
.error-input {
  border-color: rgba(239, 68, 68, 1) !important;
  box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3);
}

.error-input:focus {
  border-color: rgba(239, 68, 68, 1) !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Styles pour le chargement */
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

:host {
  display: flex;
  align-items: center;        /* centre verticalement */
  justify-content: center;    /* centre horizontalement */
  min-height: 100vh;          /* prend toute la hauteur de la fenêtre */
  padding: 1rem;              /* petit padding si besoin */
}
